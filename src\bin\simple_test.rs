//! 简单的两轮驱动测试程序
//! 这个程序只测试基本的前进、后退和停止功能

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

// 引入本地的drv8833模块
mod drv8833 {
    use embassy_stm32::timer::GeneralInstance4Channel;
    use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

    #[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, defmt::Format)]
    pub enum Error {
        InvalidSpeed,
    }

    #[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
    pub enum Direction {
        Forward,
        Backward,
        Brake,
        Coast,
    }

    pub struct Drv8833Single<TIM: GeneralInstance4Channel> {
        in1: SimplePwmChannel<'static, TIM>,
        in2: SimplePwmChannel<'static, TIM>,
    }

    impl<TIM: GeneralInstance4Channel> Drv8833Single<TIM> {
        pub fn new(in1: SimplePwmChannel<'static, TIM>, in2: SimplePwmChannel<'static, TIM>) -> Self {
            Self { in1, in2 }
        }

        pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
            if speed > 100 {
                return Err(Error::InvalidSpeed);
            }

            match direction {
                Direction::Forward => {
                    let max_duty = self.in1.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(duty as u16);
                    self.in2.set_duty_cycle(0);
                }
                Direction::Backward => {
                    let max_duty = self.in2.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(duty as u16);
                }
                Direction::Brake => {
                    let max_duty = self.in1.max_duty_cycle();
                    self.in1.set_duty_cycle(max_duty);
                    self.in2.set_duty_cycle(max_duty);
                }
                Direction::Coast => {
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(0);
                }
            }
            Ok(())
        }

        pub fn coast(&mut self) -> Result<(), Error> {
            self.set_motor(Direction::Coast, 0)
        }
    }
}

use drv8833::{Direction, Drv8833Single};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 Simple Two-Wheel Drive Test Starting");

    // 初始化PWM引脚
    let left_pwm_pin1 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);
    let left_pwm_pin2 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let right_pwm_pin1 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);
    let right_pwm_pin2 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);

    // 创建PWM实例
    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(left_pwm_pin1),
        Some(left_pwm_pin2),
        Some(right_pwm_pin1),
        Some(right_pwm_pin2),
        Hertz(10_000),
        Default::default(),
    );

    let tim2_chs = pwm_tim2.split();

    // 获取并启用PWM通道
    let mut left_pwm_in1 = tim2_chs.ch1;
    let mut left_pwm_in2 = tim2_chs.ch2;
    let mut right_pwm_in1 = tim2_chs.ch3;
    let mut right_pwm_in2 = tim2_chs.ch4;

    left_pwm_in1.enable();
    left_pwm_in2.enable();
    right_pwm_in1.enable();
    right_pwm_in2.enable();

    // 创建电机驱动器
    let mut left_motor = Drv8833Single::new(left_pwm_in1, left_pwm_in2);
    let mut right_motor = Drv8833Single::new(right_pwm_in1, right_pwm_in2);

    info!("✅ Motors initialized successfully");

    loop {
        // 简单的前进测试
        info!("🔄 Forward test");
        let _ = left_motor.set_motor(Direction::Forward, 30);
        let _ = right_motor.set_motor(Direction::Forward, 30);
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ Stop");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 简单的后退测试
        info!("🔄 Backward test");
        let _ = left_motor.set_motor(Direction::Backward, 30);
        let _ = right_motor.set_motor(Direction::Backward, 30);
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ Stop");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(2)).await;

        info!("✅ Test cycle completed");
    }
}
