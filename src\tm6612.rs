//! TM6612 双 H 桥电机驱动器实现
//!
//! TM6612 是一个双H桥电机驱动器，类似于DRV8833
//! 支持两个直流电机的独立控制

use embassy_stm32::timer::GeneralInstance4Channel;
use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

/// TM6612 驱动错误
#[derive(Debug, Clone, Copy, PartialEq, Eq, defmt::Format)]
pub enum Error {
    /// 无效的速度值 (必须在 0-100 之间)
    InvalidSpeed,
}

/// 电机方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 刹车 (两引脚都为高电平)
    Brake,
    /// 滑行 (两引脚都为低电平)
    Coast,
}

/// TM6612 单电机驱动器
///
/// TM6612 的控制方式与 DRV8833 相同：
/// - 使用两个 PWM 通道 (IN1, IN2)
/// - 通过 PWM 占空比控制速度和方向
///
/// # 控制原理
/// ```text
/// 前进: IN1=PWM(speed), IN2=0%
/// 后退: IN1=0%, IN2=PWM(speed)
/// 刹车: IN1=100%, IN2=100%
/// 滑行: IN1=0%, IN2=0%
/// ```
pub struct Tm6612Single<TIM: GeneralInstance4Channel> {
    pub in1: SimplePwmChannel<'static, TIM>,
    pub in2: SimplePwmChannel<'static, TIM>,
}

impl<TIM: GeneralInstance4Channel> Tm6612Single<TIM> {
    /// 创建新的 TM6612 单电机驱动器
    ///
    /// # 参数
    /// * `in1` - 电机 IN1 引脚的 PWM 通道
    /// * `in2` - 电机 IN2 引脚的 PWM 通道
    pub fn new(in1: SimplePwmChannel<'static, TIM>, in2: SimplePwmChannel<'static, TIM>) -> Self {
        Self { in1, in2 }
    }

    /// 设置电机方向和速度
    ///
    /// # 参数
    /// * `direction` - 电机方向
    /// * `speed` - 速度百分比 (0-100)
    pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
        if speed > 100 {
            return Err(Error::InvalidSpeed);
        }

        match direction {
            Direction::Forward => {
                let max_duty = self.in1.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(duty as u16);
                self.in2.set_duty_cycle(0);
            }
            Direction::Backward => {
                let max_duty = self.in2.max_duty_cycle();
                let duty = (max_duty as u32 * speed as u32) / 100;
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(duty as u16);
            }
            Direction::Brake => {
                let max_duty = self.in1.max_duty_cycle();
                self.in1.set_duty_cycle(max_duty);
                self.in2.set_duty_cycle(max_duty);
            }
            Direction::Coast => {
                self.in1.set_duty_cycle(0);
                self.in2.set_duty_cycle(0);
            }
        }

        Ok(())
    }

    /// 刹车电机
    pub fn brake(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Brake, 0)
    }

    /// 滑行停止电机
    pub fn coast(&mut self) -> Result<(), Error> {
        self.set_motor(Direction::Coast, 0)
    }
}
