//! 统一的电机驱动器接口和麦克纳姆轮驱动系统

use embassy_stm32::timer::GeneralInstance4Channel;

/// 统一的电机驱动器错误类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, defmt::Format)]
pub enum MotorError {
    /// 无效的速度值
    InvalidSpeed,
    /// 驱动器错误
    DriverError,
}

/// 电机方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Direction {
    /// 前进
    Forward,
    /// 后退
    Backward,
    /// 刹车
    Brake,
    /// 滑行
    Coast,
}

/// 麦克纳姆轮运动方向
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MecanumDirection {
    /// 向前
    Forward,
    /// 向后
    Backward,
    /// 向左
    Left,
    /// 向右
    Right,
    /// 顺时针旋转
    RotateClockwise,
    /// 逆时针旋转
    RotateCounterClockwise,
    /// 左前对角线
    ForwardLeft,
    /// 右前对角线
    ForwardRight,
    /// 左后对角线
    BackwardLeft,
    /// 右后对角线
    BackwardRight,
}

/// 通用电机驱动器接口
pub trait MotorDriver {
    /// 设置电机方向和速度
    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), MotorError>;

    /// 刹车电机
    fn brake(&mut self) -> Result<(), MotorError>;

    /// 滑行停止电机
    fn coast(&mut self) -> Result<(), MotorError>;
}

/// DRV8833 驱动器包装
pub struct Drv8833Wrapper<TIM: GeneralInstance4Channel> {
    driver: crate::drv8833::Drv8833Single<TIM>,
}

impl<TIM: GeneralInstance4Channel> Drv8833Wrapper<TIM> {
    pub fn new(driver: crate::drv8833::Drv8833Single<TIM>) -> Self {
        Self { driver }
    }
}

impl<TIM: GeneralInstance4Channel> MotorDriver for Drv8833Wrapper<TIM> {
    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), MotorError> {
        let drv_direction = match direction {
            Direction::Forward => crate::drv8833::Direction::Forward,
            Direction::Backward => crate::drv8833::Direction::Backward,
            Direction::Brake => crate::drv8833::Direction::Brake,
            Direction::Coast => crate::drv8833::Direction::Coast,
        };

        self.driver
            .set_motor(drv_direction, speed)
            .map_err(|_| MotorError::DriverError)
    }

    fn brake(&mut self) -> Result<(), MotorError> {
        self.driver.brake().map_err(|_| MotorError::DriverError)
    }

    fn coast(&mut self) -> Result<(), MotorError> {
        self.driver.coast().map_err(|_| MotorError::DriverError)
    }
}

/// TM6612 驱动器包装
pub struct Tm6612Wrapper<'a, TIM: GeneralInstance4Channel> {
    driver: crate::tm6612::Tm6612Single<'a, TIM>,
}

impl<'a, TIM: GeneralInstance4Channel> Tm6612Wrapper<'a, TIM> {
    pub fn new(driver: crate::tm6612::Tm6612Single<'a, TIM>) -> Self {
        Self { driver }
    }
}

impl<'a, TIM: GeneralInstance4Channel> MotorDriver for Tm6612Wrapper<'a, TIM> {
    fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), MotorError> {
        let tm_direction = match direction {
            Direction::Forward => crate::tm6612::Direction::Forward,
            Direction::Backward => crate::tm6612::Direction::Backward,
            Direction::Brake => crate::tm6612::Direction::Brake,
            Direction::Coast => crate::tm6612::Direction::Coast,
        };

        self.driver
            .set_motor(tm_direction, speed)
            .map_err(|_| MotorError::DriverError)
    }

    fn brake(&mut self) -> Result<(), MotorError> {
        self.driver.brake().map_err(|_| MotorError::DriverError)
    }

    fn coast(&mut self) -> Result<(), MotorError> {
        self.driver.coast().map_err(|_| MotorError::DriverError)
    }
}

/// 麦克纳姆轮驱动系统
pub struct MecanumDrive<FL, FR, BL, BR>
where
    FL: MotorDriver,
    FR: MotorDriver,
    BL: MotorDriver,
    BR: MotorDriver,
{
    front_left: FL,
    front_right: FR,
    back_left: BL,
    back_right: BR,
}

impl<FL, FR, BL, BR> MecanumDrive<FL, FR, BL, BR>
where
    FL: MotorDriver,
    FR: MotorDriver,
    BL: MotorDriver,
    BR: MotorDriver,
{
    /// 创建新的麦克纳姆轮驱动系统
    pub fn new(front_left: FL, front_right: FR, back_left: BL, back_right: BR) -> Self {
        Self {
            front_left,
            front_right,
            back_left,
            back_right,
        }
    }

    /// 按指定方向移动
    pub fn move_direction(
        &mut self,
        direction: MecanumDirection,
        speed: u8,
    ) -> Result<(), MotorError> {
        if speed > 100 {
            return Err(MotorError::InvalidSpeed);
        }

        match direction {
            MecanumDirection::Forward => {
                self.front_left.set_motor(Direction::Forward, speed)?;
                self.front_right.set_motor(Direction::Forward, speed)?;
                self.back_left.set_motor(Direction::Forward, speed)?;
                self.back_right.set_motor(Direction::Forward, speed)?;
            }
            MecanumDirection::Backward => {
                self.front_left.set_motor(Direction::Backward, speed)?;
                self.front_right.set_motor(Direction::Backward, speed)?;
                self.back_left.set_motor(Direction::Backward, speed)?;
                self.back_right.set_motor(Direction::Backward, speed)?;
            }
            MecanumDirection::Left => {
                self.front_left.set_motor(Direction::Backward, speed)?;
                self.front_right.set_motor(Direction::Forward, speed)?;
                self.back_left.set_motor(Direction::Forward, speed)?;
                self.back_right.set_motor(Direction::Backward, speed)?;
            }
            MecanumDirection::Right => {
                self.front_left.set_motor(Direction::Forward, speed)?;
                self.front_right.set_motor(Direction::Backward, speed)?;
                self.back_left.set_motor(Direction::Backward, speed)?;
                self.back_right.set_motor(Direction::Forward, speed)?;
            }
            MecanumDirection::RotateClockwise => {
                self.front_left.set_motor(Direction::Forward, speed)?;
                self.front_right.set_motor(Direction::Backward, speed)?;
                self.back_left.set_motor(Direction::Forward, speed)?;
                self.back_right.set_motor(Direction::Backward, speed)?;
            }
            MecanumDirection::RotateCounterClockwise => {
                self.front_left.set_motor(Direction::Backward, speed)?;
                self.front_right.set_motor(Direction::Forward, speed)?;
                self.back_left.set_motor(Direction::Backward, speed)?;
                self.back_right.set_motor(Direction::Forward, speed)?;
            }
            MecanumDirection::ForwardLeft => {
                self.front_left.set_motor(Direction::Coast, 0)?;
                self.front_right.set_motor(Direction::Forward, speed)?;
                self.back_left.set_motor(Direction::Forward, speed)?;
                self.back_right.set_motor(Direction::Coast, 0)?;
            }
            MecanumDirection::ForwardRight => {
                self.front_left.set_motor(Direction::Forward, speed)?;
                self.front_right.set_motor(Direction::Coast, 0)?;
                self.back_left.set_motor(Direction::Coast, 0)?;
                self.back_right.set_motor(Direction::Forward, speed)?;
            }
            MecanumDirection::BackwardLeft => {
                self.front_left.set_motor(Direction::Backward, speed)?;
                self.front_right.set_motor(Direction::Coast, 0)?;
                self.back_left.set_motor(Direction::Coast, 0)?;
                self.back_right.set_motor(Direction::Backward, speed)?;
            }
            MecanumDirection::BackwardRight => {
                self.front_left.set_motor(Direction::Coast, 0)?;
                self.front_right.set_motor(Direction::Backward, speed)?;
                self.back_left.set_motor(Direction::Backward, speed)?;
                self.back_right.set_motor(Direction::Coast, 0)?;
            }
        }

        Ok(())
    }

    /// 停止所有电机
    pub fn stop_all(&mut self) -> Result<(), MotorError> {
        self.front_left.coast()?;
        self.front_right.coast()?;
        self.back_left.coast()?;
        self.back_right.coast()?;
        Ok(())
    }

    /// 刹车所有电机
    pub fn brake_all(&mut self) -> Result<(), MotorError> {
        self.front_left.brake()?;
        self.front_right.brake()?;
        self.back_left.brake()?;
        self.back_right.brake()?;
        Ok(())
    }
}
