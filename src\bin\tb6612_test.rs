//! TB6612 驱动器测试程序
//! 测试TB6612的独立PWM控制方式

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

// TB6612 驱动器实现
mod tb6612 {
    use embassy_stm32::gpio::{Level, Output};
    use embassy_stm32::timer::GeneralInstance4Channel;
    use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

    #[derive(Debug, Clone, Copy, PartialEq, Eq, defmt::Format)]
    pub enum Error {
        InvalidSpeed,
    }

    #[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
    pub enum Direction {
        Forward,
        Backward,
        Brake,
        Coast,
    }

    pub struct Tb6612Single<'a, TIM: GeneralInstance4Channel> {
        pub in1: Output<'a>,
        pub in2: Output<'a>,
        pub pwm: SimplePwmChannel<'static, TIM>,
    }

    impl<'a, TIM: GeneralInstance4Channel> Tb6612Single<'a, TIM> {
        pub fn new(in1: Output<'a>, in2: Output<'a>, pwm: SimplePwmChannel<'static, TIM>) -> Self {
            Self { in1, in2, pwm }
        }

        pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
            if speed > 100 {
                return Err(Error::InvalidSpeed);
            }

            // 设置PWM速度
            let max_duty = self.pwm.max_duty_cycle();
            let duty = if direction == Direction::Coast {
                0
            } else {
                (max_duty as u32 * speed as u32) / 100
            };
            self.pwm.set_duty_cycle(duty as u16);

            // 设置方向控制引脚
            match direction {
                Direction::Forward => {
                    self.in1.set_high();
                    self.in2.set_low();
                }
                Direction::Backward => {
                    self.in1.set_low();
                    self.in2.set_high();
                }
                Direction::Brake => {
                    self.in1.set_high();
                    self.in2.set_high();
                }
                Direction::Coast => {
                    self.in1.set_low();
                    self.in2.set_low();
                }
            }

            Ok(())
        }

        pub fn coast(&mut self) -> Result<(), Error> {
            self.set_motor(Direction::Coast, 0)
        }
    }
}

use tb6612::{Direction, Tb6612Single};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🔧 TB6612 驱动器测试开始");

    // 初始化TB6612控制引脚
    // 方向控制引脚
    let in1 = Output::new(p.PB0, Level::Low, Speed::Low);
    let in2 = Output::new(p.PB1, Level::Low, Speed::Low);
    
    // PWM速度控制引脚 (使用TIM3_CH1 - PA6)
    let pwm_pin = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);

    // 创建PWM实例
    let pwm = SimplePwm::new(
        p.TIM3,
        Some(pwm_pin),
        None,
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );

    let chs = pwm.split();
    let mut pwm_ch = chs.ch1;
    pwm_ch.enable();

    // 创建TB6612驱动器
    let mut motor = Tb6612Single::new(in1, in2, pwm_ch);

    info!("✅ TB6612 初始化完成");
    info!("📍 引脚配置:");
    info!("  - IN1: PB0 (方向控制)");
    info!("  - IN2: PB1 (方向控制)");
    info!("  - PWM: PA6 (速度控制)");

    loop {
        // 前进测试
        info!("🔄 前进测试 (30% 速度)");
        match motor.set_motor(Direction::Forward, 30) {
            Ok(_) => info!("  ✅ 前进成功"),
            Err(e) => error!("  ❌ 前进失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 后退测试
        info!("🔄 后退测试 (30% 速度)");
        match motor.set_motor(Direction::Backward, 30) {
            Ok(_) => info!("  ✅ 后退成功"),
            Err(e) => error!("  ❌ 后退失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 刹车测试
        info!("🛑 刹车测试");
        match motor.set_motor(Direction::Brake, 0) {
            Ok(_) => info!("  ✅ 刹车成功"),
            Err(e) => error!("  ❌ 刹车失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(1)).await;

        // 高速前进测试
        info!("🔄 高速前进测试 (70% 速度)");
        match motor.set_motor(Direction::Forward, 70) {
            Ok(_) => info!("  ✅ 高速前进成功"),
            Err(e) => error!("  ❌ 高速前进失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 最终停止
        info!("⏹️ 最终停止");
        let _ = motor.coast();
        Timer::after(Duration::from_secs(2)).await;

        info!("✅ TB6612 测试周期完成");
        Timer::after(Duration::from_secs(1)).await;
    }
}
