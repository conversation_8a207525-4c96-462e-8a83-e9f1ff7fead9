#![no_std]
#![no_main]

mod drv8833;

use core::cmp::max;

use defmt::*;
use drv8833::{Direction, Drv8833Single};
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    // 初始化两个电机的PWM通道
    // 左轮电机 - 使用 PA0, PA1
    let left_pwm_pin1 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);
    let left_pwm_pin2 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);

    // 右轮电机 - 使用 PA2, PA3
    let right_pwm_pin1 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let right_pwm_pin2 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);

    let pwm_tim3 = SimplePwm::new(
        p.TIM2,
        Some(right_pwm_pin2),
        Some(right_pwm_pin1),
        Some(left_pwm_pin1),
        Some(left_pwm_pin2),
        Hertz(10_000), // 10kHz PWM频率
        Default::default(),
    );
    let tim3_chs = pwm_tim3.split();

    // 获取PWM通道
    let mut left_pwm_in1 = tim3_chs.ch1;
    let mut left_pwm_in2 = tim3_chs.ch2;
    let mut right_pwm_in1 = tim3_chs.ch3;
    let mut right_pwm_in2 = tim3_chs.ch4;

    // 启用PWM通道
    left_pwm_in1.enable();
    left_pwm_in2.enable();
    right_pwm_in1.enable();
    right_pwm_in2.enable();

    // 创建两个独立的电机驱动器
    let mut left_motor = Drv8833Single::new(left_pwm_in1, left_pwm_in2);
    let mut right_motor = Drv8833Single::new(right_pwm_in1, right_pwm_in2);

    info!("Two-wheel drive initialized, starting movement demo");

    loop {
        // 向前运动 - 两轮同时前进
        info!("Moving forward");
        match (
            left_motor.set_motor(Direction::Forward, 80),
            right_motor.set_motor(Direction::Forward, 80),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Forward motion successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Forward motion failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("Stopping");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 向后运动 - 两轮同时后退
        info!("Moving backward");
        match (
            left_motor.set_motor(Direction::Backward, 80),
            right_motor.set_motor(Direction::Backward, 80),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Backward motion successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Backward motion failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("Stopping");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 右转 - 左轮前进，右轮后退
        info!("Turning right");
        match (
            left_motor.set_motor(Direction::Forward, 80),
            right_motor.set_motor(Direction::Backward, 80),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Right turn successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Right turn failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("Stopping");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 左转 - 右轮前进，左轮后退
        info!("Turning left");
        match (
            left_motor.set_motor(Direction::Backward, 80),
            right_motor.set_motor(Direction::Forward, 80),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Left turn successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Left turn failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 刹车测试
        info!("Braking");
        let _ = left_motor.brake();
        let _ = right_motor.brake();
        Timer::after(Duration::from_secs(1)).await;

        // 大弧度右转 - 左轮快速前进，右轮慢速前进
        info!("Wide right turn");
        match (
            left_motor.set_motor(Direction::Forward, 70),
            right_motor.set_motor(Direction::Forward, 30),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Wide right turn successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Wide right turn failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 大弧度左转 - 右轮快速前进，左轮慢速前进
        info!("Wide left turn");
        match (
            left_motor.set_motor(Direction::Forward, 30),
            right_motor.set_motor(Direction::Forward, 70),
        ) {
            (Ok(_), Ok(_)) => info!("  ✅ Wide left turn successful"),
            (Err(e), _) | (_, Err(e)) => error!("  ❌ Wide left turn failed: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 最终停止
        info!("Final stop");
        let _ = left_motor.coast();
        let _ = right_motor.coast();
        Timer::after(Duration::from_secs(2)).await;

        info!("✅ Movement cycle completed");
        Timer::after(Duration::from_secs(1)).await;
    }
}
