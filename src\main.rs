#![no_std]
#![no_main]

mod drv8833;
mod motor_driver;
mod tm6612;

use defmt::*;
use drv8833::Drv8833Single;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use motor_driver::{Drv8833Wrapper, MecanumDirection, MecanumDrive, Tm6612Wrapper};
use tm6612::Tm6612Single;
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 四轮麦克纳姆驱动系统初始化开始");

    // 初始化四个电机的PWM通道
    // 前左轮电机 (DRV8833) - 使用 PA0, PA1
    let fl_pwm_pin1 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);
    let fl_pwm_pin2 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);

    // 前右轮电机 (DRV8833) - 使用 PA2, PA3
    let fr_pwm_pin1 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pwm_pin2 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);

    // 后左轮电机 (TM6612) - 使用 PA6, PA7
    let bl_pwm_pin1 = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::new(p.PA7, embassy_stm32::gpio::OutputType::PushPull);

    // 后右轮电机 (TM6612) - 使用 PB0, PB1
    let br_pwm_pin1 = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);

    // 创建PWM实例 - 使用TIM2的4个通道 (前轮)
    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(fl_pwm_pin1),
        Some(fl_pwm_pin2),
        Some(fr_pwm_pin1),
        Some(fr_pwm_pin2),
        Hertz(10_000), // 10kHz PWM频率
        Default::default(),
    );

    // 创建PWM实例 - 使用TIM3的4个通道 (后轮)
    let pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_pwm_pin1),
        Some(bl_pwm_pin2),
        Some(br_pwm_pin1),
        Some(br_pwm_pin2),
        Hertz(10_000), // 10kHz PWM频率
        Default::default(),
    );

    let tim2_chs = pwm_tim2.split();
    let tim3_chs = pwm_tim3.split();

    // 获取并启用前轮PWM通道
    let mut fl_pwm_in1 = tim2_chs.ch1;
    let mut fl_pwm_in2 = tim2_chs.ch2;
    let mut fr_pwm_in1 = tim2_chs.ch3;
    let mut fr_pwm_in2 = tim2_chs.ch4;

    fl_pwm_in1.enable();
    fl_pwm_in2.enable();
    fr_pwm_in1.enable();
    fr_pwm_in2.enable();

    // 获取并启用后轮PWM通道
    let mut bl_pwm_in1 = tim3_chs.ch1;
    let mut bl_pwm_in2 = tim3_chs.ch2;
    let mut br_pwm_in1 = tim3_chs.ch3;
    let mut br_pwm_in2 = tim3_chs.ch4;

    bl_pwm_in1.enable();
    bl_pwm_in2.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    // 创建电机驱动器实例
    let front_left_drv = Drv8833Single::new(fl_pwm_in1, fl_pwm_in2);
    let front_right_drv = Drv8833Single::new(fr_pwm_in1, fr_pwm_in2);
    let back_left_tm = Tm6612Single::new(bl_pwm_in1, bl_pwm_in2);
    let back_right_tm = Tm6612Single::new(br_pwm_in1, br_pwm_in2);

    // 包装驱动器以实现统一接口
    let front_left = Drv8833Wrapper::new(front_left_drv);
    let front_right = Drv8833Wrapper::new(front_right_drv);
    let back_left = Tm6612Wrapper::new(back_left_tm);
    let back_right = Tm6612Wrapper::new(back_right_tm);

    // 创建麦克纳姆轮驱动系统
    let mut mecanum = MecanumDrive::new(front_left, front_right, back_left, back_right);

    info!("✅ 四轮麦克纳姆驱动系统初始化完成");
    info!("🔧 前轮使用DRV8833，后轮使用TM6612");
    info!("🎯 开始运动演示...");

    loop {
        // 1. 向前运动
        info!("🔄 向前运动");
        match mecanum.move_direction(MecanumDirection::Forward, 50) {
            Ok(_) => info!("  ✅ 向前运动成功"),
            Err(e) => error!("  ❌ 向前运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 2. 向后运动
        info!("🔄 向后运动");
        match mecanum.move_direction(MecanumDirection::Backward, 50) {
            Ok(_) => info!("  ✅ 向后运动成功"),
            Err(e) => error!("  ❌ 向后运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 3. 向左平移 (麦克纳姆轮特有)
        info!("🔄 向左平移");
        match mecanum.move_direction(MecanumDirection::Left, 50) {
            Ok(_) => info!("  ✅ 向左平移成功"),
            Err(e) => error!("  ❌ 向左平移失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 4. 向右平移 (麦克纳姆轮特有)
        info!("🔄 向右平移");
        match mecanum.move_direction(MecanumDirection::Right, 50) {
            Ok(_) => info!("  ✅ 向右平移成功"),
            Err(e) => error!("  ❌ 向右平移失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 5. 顺时针旋转
        info!("🔄 顺时针旋转");
        match mecanum.move_direction(MecanumDirection::RotateClockwise, 40) {
            Ok(_) => info!("  ✅ 顺时针旋转成功"),
            Err(e) => error!("  ❌ 顺时针旋转失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 6. 逆时针旋转
        info!("🔄 逆时针旋转");
        match mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 40) {
            Ok(_) => info!("  ✅ 逆时针旋转成功"),
            Err(e) => error!("  ❌ 逆时针旋转失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 7. 左前对角线运动
        info!("🔄 左前对角线运动");
        match mecanum.move_direction(MecanumDirection::ForwardLeft, 50) {
            Ok(_) => info!("  ✅ 左前对角线运动成功"),
            Err(e) => error!("  ❌ 左前对角线运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 8. 右前对角线运动
        info!("🔄 右前对角线运动");
        match mecanum.move_direction(MecanumDirection::ForwardRight, 50) {
            Ok(_) => info!("  ✅ 右前对角线运动成功"),
            Err(e) => error!("  ❌ 右前对角线运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 最终刹车
        info!("🛑 最终刹车");
        let _ = mecanum.brake_all();
        Timer::after(Duration::from_secs(1)).await;

        info!("✅ 麦克纳姆轮运动演示周期完成");
        info!("🔄 等待下一个周期...");
        Timer::after(Duration::from_secs(2)).await;
    }
}
