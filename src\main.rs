#![no_std]
#![no_main]

mod drv8833;
mod motor_driver;
mod tm6612;

use defmt::*;
use drv8833::Drv8833Single;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use motor_driver::{Drv8833Wrapper, MecanumDirection, MecanumDrive, Tm6612Wrapper};
use tm6612::Tm6612Single;
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 四轮麦克纳姆驱动系统初始化开始");

    // 初始化四个电机的PWM通道
    // 前左轮电机 (TB6612) - 方向控制: PA0, PA1, PWM控制: PA2
    let fl_in1 = Output::new(p.PA4, Level::Low, Speed::Low);
    let fl_in2 = Output::new(p.PA5, Level::Low, Speed::Low);
    let fl_pwm_pin = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);

    // 前右轮电机 (TB6612) - 方向控制: PA3, PA4, PWM控制: PA5
    let fr_in1 = Output::new(p.PA7, Level::Low, Speed::Low);
    let fr_in2 = Output::new(p.PA6, Level::Low, Speed::Low);
    let fr_pwm_pin = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);

    // 后左轮电机 (DRV8833) - 使用 PA6, PA7
    let bl_pwm_pin1 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);

    // 后右轮电机 (DRV8833) - 使用 PB0, PB1
    let br_pwm_pin1 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);

    // 创建PWM实例 - 使用TIM2和TIM3的通道
    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(bl_pwm_pin2), // 后左轮PWM2 (DRV8833)
        Some(bl_pwm_pin1), // 后左轮PWM1 (DRV8833)
        Some(br_pwm_pin2), // 后右轮PWM1 (DRV8833)
        Some(br_pwm_pin1), // 后右轮PWM2 (DRV8833)
        Hertz(20_000),     // 20kHz PWM频率
        Default::default(),
    );

    let pwm_tim3 = SimplePwm::new(
        p.TIM3,
        None,
        None,
        Some(fl_pwm_pin), // 前左轮PWM (TB6612)
        Some(fr_pwm_pin), // 前右轮PWM (TB6612)
        Hertz(20_000),    // 20kHz PWM频率
        Default::default(),
    );

    let tim3_chs = pwm_tim3.split();
    let tim2_chs = pwm_tim2.split();

    // 获取并启用PWM通道
    let mut fl_pwm = tim3_chs.ch3; // 前左轮PWM
    let mut fr_pwm = tim3_chs.ch4; // 前右轮PWM

    let mut bl_pwm_in1 = tim2_chs.ch2; // 后左轮PWM2
    let mut bl_pwm_in2 = tim2_chs.ch1; // 后左轮PWM1
    let mut br_pwm_in1 = tim2_chs.ch4; // 后右轮PWM1
    let mut br_pwm_in2 = tim2_chs.ch3; // 后右轮PWM2

    // 启用所有PWM通道
    fl_pwm.enable();
    fr_pwm.enable();
    bl_pwm_in2.enable();
    bl_pwm_in1.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    // 创建电机驱动器实例
    let front_left_tm = Tm6612Single::new(fl_in1, fl_in2, fl_pwm);
    let front_right_tm = Tm6612Single::new(fr_in1, fr_in2, fr_pwm);
    let back_left_drv = Drv8833Single::new(bl_pwm_in2, bl_pwm_in1);
    let back_right_drv = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

    // 包装驱动器以实现统一接口
    let front_left = Tm6612Wrapper::new(front_left_tm);
    let front_right = Tm6612Wrapper::new(front_right_tm);
    let back_left = Drv8833Wrapper::new(back_left_drv);
    let back_right = Drv8833Wrapper::new(back_right_drv);

    // 创建麦克纳姆轮驱动系统
    let mut mecanum = MecanumDrive::new(front_left, front_right, back_left, back_right);

    info!("✅ 四轮麦克纳姆驱动系统初始化完成");
    info!("🔧 前轮使用TB6612，后轮使用DRV8833");
    info!("🎯 开始运动演示...");

    Timer::after_secs(2).await;
    loop {
        // 1. 向前运动
        info!("🔄 向前运动");
        match mecanum.move_direction(MecanumDirection::Forward, 50) {
            Ok(_) => info!("  ✅ 向前运动成功"),
            Err(e) => error!("  ❌ 向前运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止``
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 2. 向后运动
        info!("🔄 向后运动");
        match mecanum.move_direction(MecanumDirection::Backward, 50) {
            Ok(_) => info!("  ✅ 向后运动成功"),
            Err(e) => error!("  ❌ 向后运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 3. 向左平移 (麦克纳姆轮特有)
        info!("🔄 向左平移");
        match mecanum.move_direction(MecanumDirection::Left, 50) {
            Ok(_) => info!("  ✅ 向左平移成功"),
            Err(e) => error!("  ❌ 向左平移失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 4. 向右平移 (麦克纳姆轮特有)
        info!("🔄 向右平移");
        match mecanum.move_direction(MecanumDirection::Right, 50) {
            Ok(_) => info!("  ✅ 向右平移成功"),
            Err(e) => error!("  ❌ 向右平移失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(3)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 5. 顺时针旋转
        info!("🔄 顺时针旋转");
        match mecanum.move_direction(MecanumDirection::RotateClockwise, 40) {
            Ok(_) => info!("  ✅ 顺时针旋转成功"),
            Err(e) => error!("  ❌ 顺时针旋转失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 6. 逆时针旋转
        info!("🔄 逆时针旋转");
        match mecanum.move_direction(MecanumDirection::RotateCounterClockwise, 40) {
            Ok(_) => info!("  ✅ 逆时针旋转成功"),
            Err(e) => error!("  ❌ 逆时针旋转失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 7. 左前对角线运动
        info!("🔄 左前对角线运动");
        match mecanum.move_direction(MecanumDirection::ForwardLeft, 50) {
            Ok(_) => info!("  ✅ 左前对角线运动成功"),
            Err(e) => error!("  ❌ 左前对角线运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;

        // 8. 右前对角线运动
        info!("🔄 右前对角线运动");
        match mecanum.move_direction(MecanumDirection::ForwardRight, 50) {
            Ok(_) => info!("  ✅ 右前对角线运动成功"),
            Err(e) => error!("  ❌ 右前对角线运动失败: {:?}", e),
        }
        Timer::after(Duration::from_secs(2)).await;

        // 最终刹车
        info!("🛑 最终刹车");
        let _ = mecanum.brake_all();
        Timer::after(Duration::from_secs(1)).await;

        info!("✅ 麦克纳姆轮运动演示周期完成");
        info!("🔄 等待下一个周期...");
        Timer::after(Duration::from_secs(2)).await;
    }
}
