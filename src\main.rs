#![no_std]
#![no_main]

use defmt::*;
use drv8833::Drv8833<PERSON><PERSON>le;
use drv8833::mecanum::{MecanumDirection, MecanumDrive};
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    // 初始化四个电动机的PWM通道
    let fl_pwm_pin1 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);
    let fl_pwm_pin2 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pwm_pin1 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pwm_pin2 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin1 = PwmPin::new(p.PA6, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::new(p.PA7, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin1 = PwmPin::new(p.PB0, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::new(p.PB1, embassy_stm32::gpio::OutputType::PushPull);

    // 创建PWM实例
    let pwm_tim2 = SimplePwm::new(
        p.TIM2,
        Some(fl_pwm_pin1),
        Some(fl_pwm_pin2),
        Some(fr_pwm_pin1),
        Some(fr_pwm_pin2),
        Hertz(10_000),
        Default::default(),
    );

    let pwm_tim3 = SimplePwm::new(
        p.TIM3,
        Some(bl_pwm_pin1),
        Some(bl_pwm_pin2),
        Some(br_pwm_pin1),
        Some(br_pwm_pin2),
        Hertz(10_000),
        Default::default(),
    );

    let tim3_chs = pwm_tim3.split();
    let tim2_chs = pwm_tim2.split();

    // 获取PWM频道
    let mut fl_pwm_in1 = tim2_chs.ch1;
    let mut fl_pwm_in2 = tim2_chs.ch2;
    let mut fr_pwm_in1 = tim2_chs.ch3;
    let mut fr_pwm_in2 = tim2_chs.ch4;

    let mut bl_pwm_in1 = tim3_chs.ch1;
    let mut bl_pwm_in2 = tim3_chs.ch2;
    let mut br_pwm_in1 = tim3_chs.ch3;
    let mut br_pwm_in2 = tim3_chs.ch4;

    fl_pwm_in1.enable();
    fl_pwm_in2.enable();
    fr_pwm_in1.enable();
    fr_pwm_in2.enable();
    bl_pwm_in1.enable();
    bl_pwm_in2.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    // 创建四个独立的电机驱动器
    let fl_motor = Drv8833Single::new(fl_pwm_in1, fl_pwm_in2);
    let fr_motor = Drv8833Single::new(fr_pwm_in1, fr_pwm_in2);
    let bl_motor = Drv8833Single::new(bl_pwm_in1, bl_pwm_in2);
    let br_motor = Drv8833Single::new(br_pwm_in1, br_pwm_in2);

    // 创建MeCanum驱动系统
    let mut mecanum = MecanumDrive::new(fl_motor, fr_motor, bl_motor, br_motor);

    info!("Mecanum drive initialized, starting movement demo");

    loop {
        // 向前运动
        info!("Moving forward");
        mecanum
            .move_direction(MecanumDirection::Forward, 50)
            .unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 扫射对
        info!("Strafing right");
        mecanum.move_direction(MecanumDirection::Right, 50).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 向后运动
        info!("Moving backward");
        mecanum
            .move_direction(MecanumDirection::Backward, 50)
            .unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 扫描离开
        info!("Strafing left");
        mecanum.move_direction(MecanumDirection::Left, 50).unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 顺时针旋转
        info!("Rotating clockwise");
        mecanum
            .move_direction(MecanumDirection::RotateClockwise, 50)
            .unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // 逆时针旋转
        info!("Rotating counter-clockwise");
        mecanum
            .move_direction(MecanumDirection::RotateCounterClockwise, 50)
            .unwrap();
        Timer::after(Duration::from_secs(2)).await;

        // // 停止
        // info!("Stopping");
        // mecanum.stop_all().unwrap();
        // Timer::after(Duration::from_secs(2)).await;
    }
}
