//! 麦克纳姆轮基本功能测试程序
//! 只测试前进、后退、左右平移和旋转

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_stm32::Config;
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

// 引入本地模块
mod drv8833 {
    use embassy_stm32::timer::GeneralInstance4Channel;
    use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

    #[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, defmt::Format)]
    pub enum Error {
        InvalidSpeed,
    }

    #[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
    pub enum Direction {
        Forward,
        Backward,
        Brake,
        Coast,
    }

    pub struct Drv8833Single<TIM: GeneralInstance4Channel> {
        pub in1: SimplePwmChannel<'static, TIM>,
        pub in2: SimplePwmChannel<'static, TIM>,
    }

    impl<TIM: GeneralInstance4Channel> Drv8833Single<TIM> {
        pub fn new(in1: SimplePwmChannel<'static, TIM>, in2: SimplePwmChannel<'static, TIM>) -> Self {
            Self { in1, in2 }
        }

        pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
            if speed > 100 {
                return Err(Error::InvalidSpeed);
            }

            match direction {
                Direction::Forward => {
                    let max_duty = self.in1.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(duty as u16);
                    self.in2.set_duty_cycle(0);
                }
                Direction::Backward => {
                    let max_duty = self.in2.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(duty as u16);
                }
                Direction::Brake => {
                    let max_duty = self.in1.max_duty_cycle();
                    self.in1.set_duty_cycle(max_duty);
                    self.in2.set_duty_cycle(max_duty);
                }
                Direction::Coast => {
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(0);
                }
            }
            Ok(())
        }

        pub fn coast(&mut self) -> Result<(), Error> {
            self.set_motor(Direction::Coast, 0)
        }
    }
}

mod tm6612 {
    use embassy_stm32::timer::GeneralInstance4Channel;
    use embassy_stm32::timer::simple_pwm::SimplePwmChannel;

    #[derive(Debug, Clone, Copy, PartialEq, Eq, defmt::Format)]
    pub enum Error {
        InvalidSpeed,
    }

    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub enum Direction {
        Forward,
        Backward,
        Brake,
        Coast,
    }

    pub struct Tm6612Single<TIM: GeneralInstance4Channel> {
        pub in1: SimplePwmChannel<'static, TIM>,
        pub in2: SimplePwmChannel<'static, TIM>,
    }

    impl<TIM: GeneralInstance4Channel> Tm6612Single<TIM> {
        pub fn new(in1: SimplePwmChannel<'static, TIM>, in2: SimplePwmChannel<'static, TIM>) -> Self {
            Self { in1, in2 }
        }

        pub fn set_motor(&mut self, direction: Direction, speed: u8) -> Result<(), Error> {
            if speed > 100 {
                return Err(Error::InvalidSpeed);
            }

            match direction {
                Direction::Forward => {
                    let max_duty = self.in1.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(duty as u16);
                    self.in2.set_duty_cycle(0);
                }
                Direction::Backward => {
                    let max_duty = self.in2.max_duty_cycle();
                    let duty = (max_duty as u32 * speed as u32) / 100;
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(duty as u16);
                }
                Direction::Brake => {
                    let max_duty = self.in1.max_duty_cycle();
                    self.in1.set_duty_cycle(max_duty);
                    self.in2.set_duty_cycle(max_duty);
                }
                Direction::Coast => {
                    self.in1.set_duty_cycle(0);
                    self.in2.set_duty_cycle(0);
                }
            }
            Ok(())
        }

        pub fn coast(&mut self) -> Result<(), Error> {
            self.set_motor(Direction::Coast, 0)
        }
    }
}

use drv8833::{Drv8833Single, Direction as DrvDirection};
use tm6612::{Tm6612Single, Direction as TmDirection};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Config::default());

    info!("🚗 麦克纳姆轮基本测试开始");

    // 初始化PWM引脚 (简化版本，只使用TIM2)
    let fl_pin1 = PwmPin::new(p.PA0, embassy_stm32::gpio::OutputType::PushPull);
    let fl_pin2 = PwmPin::new(p.PA1, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pin1 = PwmPin::new(p.PA2, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pin2 = PwmPin::new(p.PA3, embassy_stm32::gpio::OutputType::PushPull);

    let pwm = SimplePwm::new(
        p.TIM2,
        Some(fl_pin1),
        Some(fl_pin2),
        Some(fr_pin1),
        Some(fr_pin2),
        Hertz(10_000),
        Default::default(),
    );

    let chs = pwm.split();
    let mut fl_ch1 = chs.ch1;
    let mut fl_ch2 = chs.ch2;
    let mut fr_ch1 = chs.ch3;
    let mut fr_ch2 = chs.ch4;

    fl_ch1.enable();
    fl_ch2.enable();
    fr_ch1.enable();
    fr_ch2.enable();

    // 创建电机驱动器 (前轮用DRV8833，后轮用TM6612模拟)
    let mut front_left = Drv8833Single::new(fl_ch1, fl_ch2);
    let mut front_right = Drv8833Single::new(fr_ch1, fr_ch2);

    info!("✅ 电机初始化完成");

    loop {
        // 简单的前进测试
        info!("🔄 前进测试");
        let _ = front_left.set_motor(DrvDirection::Forward, 40);
        let _ = front_right.set_motor(DrvDirection::Forward, 40);
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = front_left.coast();
        let _ = front_right.coast();
        Timer::after(Duration::from_secs(1)).await;

        // 简单的后退测试
        info!("🔄 后退测试");
        let _ = front_left.set_motor(DrvDirection::Backward, 40);
        let _ = front_right.set_motor(DrvDirection::Backward, 40);
        Timer::after(Duration::from_secs(2)).await;

        // 停止
        info!("⏹️ 停止");
        let _ = front_left.coast();
        let _ = front_right.coast();
        Timer::after(Duration::from_secs(2)).await;

        info!("✅ 测试周期完成");
    }
}
