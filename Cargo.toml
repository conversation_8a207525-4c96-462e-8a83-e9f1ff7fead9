[package]
authors = ["Alddp <<EMAIL>>"]
edition = "2024"
readme = "README.md"
name = "four_compitent"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[dependencies]
embassy-stm32 = { version = "0.2.0", features = [
    "defmt",
    "stm32f103c8",
    "unstable-pac",
    "memory-x",
    "time-driver-any",

], git = "https://github.com/embassy-rs/embassy.git" }
embassy-sync = { version = "0.7.0", features = [
    "defmt",
], git = "https://github.com/embassy-rs/embassy.git" }
embassy-executor = { version = "0.7.0", features = [
    "arch-cortex-m",
    "executor-thread",
    "defmt",
], git = "https://github.com/embassy-rs/embassy.git" }
embassy-time = { version = "0.4.0", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
], git = "https://github.com/embassy-rs/embassy.git" }
embassy-usb = { version = "0.5.0", features = [
    "defmt",
], git = "https://github.com/embassy-rs/embassy.git" }
embassy-futures = { version = "0.1.1", git = "https://github.com/embassy-rs/embassy.git" }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.7", features = [
    "inline-asm",
    "critical-section-single-core",
] }
cortex-m-rt = "0.7.5"
embedded-hal = "1.0.0"
panic-probe = { version = "1.0.0", features = ["print-defmt"] }
heapless = { version = "0.8.0", default-features = false }
nb = "1.1.0"
static_cell = "2.1.1"
# ==================== local ==================== 

embedded-graphics = "0.8.1"
pid = "4.0.0"
hcsr04 = { path = "../../library/hcsr04" }
mpu6500 = { git = "https://github.com/Alddp/mpu6500-rs.git" }
l298n = { git = "https://github.com/Alddp/L298N-rs.git" }
micromath = "2.1.0"

[[bin]]
name = "four_compitent"
test = false
bench = false

[[bin]]
name = "simple_test"
test = false
bench = false

[[bin]]
name = "mecanum_test"
test = false
bench = false

[lib]
test = false
bench = false

[profile.dev]
opt-level = "s"
lto = "off"
overflow-checks = true
panic = "unwind"

[profile.release]
opt-level = "s"
debug = true
codegen-units = 1
overflow-checks = false
lto = true
incremental = false
